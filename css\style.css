/* Custom CSS for Humanity Praxis Website */

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* Custom hover effects */
.hover-scale:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.hover-shadow:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transition: box-shadow 0.3s ease;
}

/* Custom button styles */
.btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300;
}

.btn-secondary {
    @apply bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300;
}

.btn-accent {
    @apply bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300;
}

.btn-outline {
    @apply border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-bold py-3 px-6 rounded-lg transition duration-300;
}

/* Custom form styles */
.form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200;
}

.form-textarea {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 resize-vertical;
}

.form-select {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 bg-white;
}

/* Custom card styles */
.card {
    @apply bg-white rounded-lg shadow-lg overflow-hidden;
}

.card-hover {
    @apply bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300;
}

/* Custom section spacing */
.section-padding {
    @apply py-16;
}

.section-padding-lg {
    @apply py-20;
}

/* Custom text styles */
.text-gradient {
    background: linear-gradient(135deg, #2563eb, #059669);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Custom utility classes */
.container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Mobile menu animation */
.mobile-menu-enter {
    opacity: 0;
    transform: translateY(-10px);
}

.mobile-menu-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.mobile-menu-exit {
    opacity: 1;
    transform: translateY(0);
}

.mobile-menu-exit-active {
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Gallery styles */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    cursor: pointer;
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

/* Modal styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    max-width: 90%;
    max-height: 90%;
    position: relative;
}

.modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    z-index: 1001;
}

/* Progress bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #2563eb, #059669);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Statistics counter animation */
.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2563eb;
}

/* Testimonial styles */
.testimonial-card {
    @apply bg-white p-6 rounded-lg shadow-lg relative;
}

.testimonial-quote {
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 3rem;
    color: #2563eb;
    opacity: 0.3;
}

/* Timeline styles */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #2563eb;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-marker {
    position: absolute;
    left: -1.5rem;
    top: 0.5rem;
    width: 1rem;
    height: 1rem;
    background: #2563eb;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 0 3px #2563eb;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .gallery-grid {
        grid-template-columns: 1fr;
    }
    
    .timeline {
        padding-left: 1rem;
    }
    
    .timeline-marker {
        left: -0.75rem;
        width: 0.75rem;
        height: 0.75rem;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
    
    img {
        max-width: 100% !important;
        height: auto !important;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .text-gray-600 {
        color: #000000;
    }
    
    .text-gray-300 {
        color: #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}
