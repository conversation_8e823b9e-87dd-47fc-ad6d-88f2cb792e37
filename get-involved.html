<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Get involved with Humanity Praxis - volunteer opportunities, internships, partnerships, and ways to make a difference in Ethiopian communities.">
    <meta name="keywords" content="volunteer Ethiopia, NGO internship, partnership opportunities, get involved, humanitarian work">
    <title>Get Involved - Humanity Praxis</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#2563eb',
                        'secondary': '#059669',
                        'accent': '#dc2626',
                        'warm': '#f59e0b',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <img src="logo.png" alt="Humanity Praxis Logo" class="h-10 w-auto">
                    <span class="ml-2 text-xl font-bold text-gray-800">Humanity Praxis</span>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="index.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Home</a>
                        <a href="about.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">About Us</a>
                        <a href="programs.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Our Programs</a>
                        <a href="get-involved.html" class="text-primary hover:text-primary-dark px-3 py-2 rounded-md text-sm font-medium">Get Involved</a>
                        <a href="donate.html" class="bg-accent text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700">Donate</a>
                        <a href="news.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">News</a>
                        <a href="gallery.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Gallery</a>
                        <a href="why-humanity.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Why Humanity</a>
                        <a href="contact.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Contact</a>
                    </div>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-gray-700 hover:text-primary focus:outline-none focus:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white shadow-lg">
                <a href="index.html" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">Home</a>
                <a href="about.html" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">About Us</a>
                <a href="programs.html" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">Our Programs</a>
                <a href="get-involved.html" class="text-primary block px-3 py-2 rounded-md text-base font-medium">Get Involved</a>
                <a href="donate.html" class="bg-accent text-white block px-3 py-2 rounded-md text-base font-medium">Donate</a>
                <a href="news.html" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">News</a>
                <a href="gallery.html" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">Gallery</a>
                <a href="why-humanity.html" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">Why Humanity</a>
                <a href="contact.html" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16">
        <!-- Hero Section -->
        <section class="bg-gradient-to-r from-primary to-secondary text-white py-16">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <h1 class="text-4xl md:text-5xl font-bold mb-4">Get Involved</h1>
                    <p class="text-xl md:text-2xl max-w-3xl mx-auto">
                        Join us in transforming lives and building stronger communities across Ethiopia
                    </p>
                </div>
            </div>
        </section>

        <!-- Ways to Get Involved -->
        <section class="py-16">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Ways to Make a Difference</h2>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                        There are many ways you can contribute to our mission and help create positive change
                    </p>
                </div>

                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div class="text-center fade-in-up">
                        <div class="bg-primary text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-hands-helping text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Volunteer</h3>
                        <p class="text-gray-600">Share your skills and time to directly impact communities</p>
                    </div>

                    <div class="text-center fade-in-up">
                        <div class="bg-secondary text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-graduation-cap text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Intern</h3>
                        <p class="text-gray-600">Gain valuable experience while contributing to meaningful work</p>
                    </div>

                    <div class="text-center fade-in-up">
                        <div class="bg-warm text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-handshake text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Partner</h3>
                        <p class="text-gray-600">Collaborate with us as an organization or business</p>
                    </div>

                    <div class="text-center fade-in-up">
                        <div class="bg-accent text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-briefcase text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">Work</h3>
                        <p class="text-gray-600">Join our team and make humanitarian work your career</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Volunteer Opportunities -->
        <section class="py-16 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Volunteer Opportunities</h2>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                        Use your skills and passion to make a direct impact in Ethiopian communities
                    </p>
                </div>

                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Education Volunteer -->
                    <div class="bg-white p-6 rounded-lg shadow-lg fade-in-up">
                        <div class="bg-warm text-white w-12 h-12 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-chalkboard-teacher text-lg"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-3">Education Volunteer</h3>
                        <p class="text-gray-600 mb-4">
                            Teach literacy, numeracy, or vocational skills to children and adults in rural communities.
                        </p>
                        <ul class="text-sm text-gray-600 mb-4 space-y-1">
                            <li>• Minimum 3-month commitment</li>
                            <li>• Teaching experience preferred</li>
                            <li>• Amharic language skills helpful</li>
                        </ul>
                        <button onclick="openVolunteerForm('education')" class="bg-warm hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded transition duration-300">
                            Apply Now
                        </button>
                    </div>

                    <!-- Healthcare Volunteer -->
                    <div class="bg-white p-6 rounded-lg shadow-lg fade-in-up">
                        <div class="bg-secondary text-white w-12 h-12 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-user-md text-lg"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-3">Healthcare Volunteer</h3>
                        <p class="text-gray-600 mb-4">
                            Support mobile health clinics and community health education programs.
                        </p>
                        <ul class="text-sm text-gray-600 mb-4 space-y-1">
                            <li>• Medical background required</li>
                            <li>• 6-month minimum commitment</li>
                            <li>• Valid medical license</li>
                        </ul>
                        <button onclick="openVolunteerForm('healthcare')" class="bg-secondary hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition duration-300">
                            Apply Now
                        </button>
                    </div>

                    <!-- Community Development -->
                    <div class="bg-white p-6 rounded-lg shadow-lg fade-in-up">
                        <div class="bg-primary text-white w-12 h-12 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-users text-lg"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-3">Community Development</h3>
                        <p class="text-gray-600 mb-4">
                            Work with communities on sustainable development and capacity building projects.
                        </p>
                        <ul class="text-sm text-gray-600 mb-4 space-y-1">
                            <li>• Development experience preferred</li>
                            <li>• 4-month minimum commitment</li>
                            <li>• Strong communication skills</li>
                        </ul>
                        <button onclick="openVolunteerForm('community')" class="bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-300">
                            Apply Now
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Internship Programs -->
        <section class="py-16">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid lg:grid-cols-2 gap-12 items-center">
                    <div class="fade-in-up">
                        <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-6">Internship Programs</h2>
                        <p class="text-lg text-gray-600 mb-6">
                            Our internship programs offer students and recent graduates the opportunity to gain
                            hands-on experience in humanitarian work while contributing to meaningful projects.
                        </p>

                        <div class="space-y-4 mb-8">
                            <div class="flex items-start">
                                <i class="fas fa-check-circle text-secondary mr-3 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Program Management Internship</h4>
                                    <p class="text-gray-600">Support project planning, implementation, and monitoring</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <i class="fas fa-check-circle text-secondary mr-3 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Communications & Marketing</h4>
                                    <p class="text-gray-600">Help with social media, content creation, and outreach</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <i class="fas fa-check-circle text-secondary mr-3 mt-1"></i>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Research & Evaluation</h4>
                                    <p class="text-gray-600">Conduct impact assessments and program evaluations</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-blue-50 p-6 rounded-lg mb-6">
                            <h4 class="font-semibold text-gray-800 mb-2">Internship Benefits:</h4>
                            <ul class="text-gray-600 space-y-1">
                                <li>• Mentorship from experienced professionals</li>
                                <li>• Certificate of completion</li>
                                <li>• Networking opportunities</li>
                                <li>• Potential for full-time employment</li>
                            </ul>
                        </div>

                        <button onclick="openInternshipForm()" class="bg-primary hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                            Apply for Internship
                        </button>
                    </div>
                    <div class="fade-in-up">
                        <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                             alt="Interns working together"
                             class="rounded-lg shadow-lg w-full h-96 object-cover">
                    </div>
                </div>
            </div>
        </section>

        <!-- Partnership Opportunities -->
        <section class="py-16 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Partnership Opportunities</h2>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                        We welcome partnerships with organizations, businesses, and institutions that share our vision
                    </p>
                </div>

                <div class="grid md:grid-cols-2 gap-8">
                    <div class="bg-white p-8 rounded-lg shadow-lg">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Corporate Partnerships</h3>
                        <p class="text-gray-600 mb-6">
                            Partner with us through corporate social responsibility programs, employee volunteering,
                            or funding specific projects that align with your company's values.
                        </p>
                        <ul class="space-y-2 text-gray-600 mb-6">
                            <li class="flex items-start">
                                <i class="fas fa-arrow-right text-primary mr-2 mt-1"></i>
                                <span>Project sponsorship opportunities</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-arrow-right text-primary mr-2 mt-1"></i>
                                <span>Employee volunteer programs</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-arrow-right text-primary mr-2 mt-1"></i>
                                <span>Skills-based volunteering</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-arrow-right text-primary mr-2 mt-1"></i>
                                <span>In-kind donations and services</span>
                            </li>
                        </ul>
                        <button onclick="openPartnershipForm('corporate')" class="bg-primary hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                            Explore Partnership
                        </button>
                    </div>

                    <div class="bg-white p-8 rounded-lg shadow-lg">
                        <h3 class="text-2xl font-bold text-gray-800 mb-4">Institutional Partnerships</h3>
                        <p class="text-gray-600 mb-6">
                            Collaborate with us as a university, research institution, or other NGO to amplify
                            our collective impact through shared resources and expertise.
                        </p>
                        <ul class="space-y-2 text-gray-600 mb-6">
                            <li class="flex items-start">
                                <i class="fas fa-arrow-right text-secondary mr-2 mt-1"></i>
                                <span>Research collaborations</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-arrow-right text-secondary mr-2 mt-1"></i>
                                <span>Student exchange programs</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-arrow-right text-secondary mr-2 mt-1"></i>
                                <span>Joint program implementation</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-arrow-right text-secondary mr-2 mt-1"></i>
                                <span>Knowledge sharing initiatives</span>
                            </li>
                        </ul>
                        <button onclick="openPartnershipForm('institutional')" class="bg-secondary hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                            Explore Partnership
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Job Opportunities -->
        <section class="py-16">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Current Job Openings</h2>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                        Join our team and make humanitarian work your career
                    </p>
                </div>

                <div class="space-y-6">
                    <!-- Job Opening 1 -->
                    <div class="bg-white p-6 rounded-lg shadow-lg border-l-4 border-primary">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                            <div class="mb-4 md:mb-0">
                                <h3 class="text-xl font-bold text-gray-800 mb-2">Program Coordinator</h3>
                                <p class="text-gray-600 mb-2">
                                    Lead implementation of education and community development programs in rural areas.
                                </p>
                                <div class="flex flex-wrap gap-2">
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">Full-time</span>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">Addis Ababa</span>
                                    <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm">3+ years experience</span>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="viewJobDetails('program-coordinator')" class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded transition duration-300">
                                    View Details
                                </button>
                                <button onclick="applyForJob('program-coordinator')" class="bg-primary hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition duration-300">
                                    Apply Now
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Job Opening 2 -->
                    <div class="bg-white p-6 rounded-lg shadow-lg border-l-4 border-secondary">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                            <div class="mb-4 md:mb-0">
                                <h3 class="text-xl font-bold text-gray-800 mb-2">Field Officer - Healthcare</h3>
                                <p class="text-gray-600 mb-2">
                                    Support mobile health clinics and community health education initiatives.
                                </p>
                                <div class="flex flex-wrap gap-2">
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">Full-time</span>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">Regional</span>
                                    <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm">Healthcare background</span>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="viewJobDetails('field-officer')" class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded transition duration-300">
                                    View Details
                                </button>
                                <button onclick="applyForJob('field-officer')" class="bg-secondary hover:bg-green-700 text-white font-medium py-2 px-4 rounded transition duration-300">
                                    Apply Now
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- No Current Openings Message -->
                    <div class="text-center py-8">
                        <p class="text-gray-600 mb-4">Don't see a position that fits your skills?</p>
                        <button onclick="openGeneralApplicationForm()" class="bg-warm hover:bg-yellow-600 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                            Submit General Application
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Forms Modal -->
        <div id="application-modal" class="modal">
            <div class="modal-content bg-white rounded-lg p-8 max-w-2xl mx-auto">
                <span class="modal-close text-gray-500 hover:text-gray-700 cursor-pointer float-right text-2xl">&times;</span>
                <div id="modal-content">
                    <!-- Dynamic content will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <section class="py-16 bg-primary text-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Ready to Make a Difference?</h2>
                <p class="text-xl mb-8 max-w-3xl mx-auto">
                    Every contribution, whether through volunteering, partnerships, or employment,
                    helps us create lasting positive change in Ethiopian communities.
                </p>
                <div class="space-x-4">
                    <a href="contact.html" class="bg-accent hover:bg-red-700 text-white font-bold py-3 px-8 rounded-lg text-lg transition duration-300">
                        Contact Us
                    </a>
                    <a href="donate.html" class="bg-transparent border-2 border-white hover:bg-white hover:text-primary text-white font-bold py-3 px-8 rounded-lg text-lg transition duration-300">
                        Support Our Work
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <img src="logo.png" alt="Humanity Praxis Logo" class="h-8 w-auto">
                        <span class="ml-2 text-lg font-bold">Humanity Praxis</span>
                    </div>
                    <p class="text-gray-300">
                        Transforming compassion into action for communities across Ethiopia.
                    </p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="about.html" class="text-gray-300 hover:text-white">About Us</a></li>
                        <li><a href="programs.html" class="text-gray-300 hover:text-white">Our Programs</a></li>
                        <li><a href="news.html" class="text-gray-300 hover:text-white">News & Updates</a></li>
                        <li><a href="contact.html" class="text-gray-300 hover:text-white">Contact Us</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Get Involved</h3>
                    <ul class="space-y-2">
                        <li><a href="donate.html" class="text-gray-300 hover:text-white">Donate</a></li>
                        <li><a href="get-involved.html" class="text-gray-300 hover:text-white">Volunteer</a></li>
                        <li><a href="get-involved.html" class="text-gray-300 hover:text-white">Partner with Us</a></li>
                        <li><a href="why-humanity.html" class="text-gray-300 hover:text-white">Why Support Us</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                    <div class="space-y-2 text-gray-300">
                        <p><i class="fas fa-map-marker-alt mr-2"></i>Addis Ababa, Ethiopia</p>
                        <p><i class="fas fa-phone mr-2"></i>+251 XXX XXX XXX</p>
                        <p><i class="fas fa-envelope mr-2"></i><EMAIL></p>
                    </div>
                    <div class="flex space-x-4 mt-4">
                        <a href="#" class="text-gray-300 hover:text-white"><i class="fab fa-facebook text-xl"></i></a>
                        <a href="#" class="text-gray-300 hover:text-white"><i class="fab fa-twitter text-xl"></i></a>
                        <a href="#" class="text-gray-300 hover:text-white"><i class="fab fa-instagram text-xl"></i></a>
                        <a href="#" class="text-gray-300 hover:text-white"><i class="fab fa-linkedin text-xl"></i></a>
                    </div>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-300">&copy; 2024 Humanity Praxis. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script>
        // Modal functionality for application forms
        function openVolunteerForm(type) {
            const modal = document.getElementById('application-modal');
            const content = document.getElementById('modal-content');

            content.innerHTML = `
                <h3 class="text-2xl font-bold text-gray-800 mb-6">Volunteer Application - ${type.charAt(0).toUpperCase() + type.slice(1)}</h3>
                <form class="space-y-4">
                    <div class="grid md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-gray-700 font-medium mb-2">First Name *</label>
                            <input type="text" required class="form-input">
                        </div>
                        <div>
                            <label class="block text-gray-700 font-medium mb-2">Last Name *</label>
                            <input type="text" required class="form-input">
                        </div>
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">Email *</label>
                        <input type="email" required class="form-input">
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">Phone Number *</label>
                        <input type="tel" required class="form-input">
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">Relevant Experience</label>
                        <textarea rows="4" class="form-textarea" placeholder="Tell us about your relevant experience and skills..."></textarea>
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">Why do you want to volunteer with us?</label>
                        <textarea rows="4" class="form-textarea" placeholder="Share your motivation and what you hope to contribute..."></textarea>
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">Availability</label>
                        <select class="form-select">
                            <option>Select your availability</option>
                            <option>3-6 months</option>
                            <option>6-12 months</option>
                            <option>1+ years</option>
                        </select>
                    </div>
                    <div class="flex space-x-4">
                        <button type="submit" class="bg-primary hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                            Submit Application
                        </button>
                        <button type="button" onclick="closeModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded-lg transition duration-300">
                            Cancel
                        </button>
                    </div>
                </form>
            `;

            modal.classList.add('active');
        }

        function openInternshipForm() {
            const modal = document.getElementById('application-modal');
            const content = document.getElementById('modal-content');

            content.innerHTML = `
                <h3 class="text-2xl font-bold text-gray-800 mb-6">Internship Application</h3>
                <form class="space-y-4">
                    <div class="grid md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-gray-700 font-medium mb-2">First Name *</label>
                            <input type="text" required class="form-input">
                        </div>
                        <div>
                            <label class="block text-gray-700 font-medium mb-2">Last Name *</label>
                            <input type="text" required class="form-input">
                        </div>
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">Email *</label>
                        <input type="email" required class="form-input">
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">University/Institution *</label>
                        <input type="text" required class="form-input">
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">Field of Study *</label>
                        <input type="text" required class="form-input">
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">Preferred Internship Area</label>
                        <select class="form-select">
                            <option>Select preferred area</option>
                            <option>Program Management</option>
                            <option>Communications & Marketing</option>
                            <option>Research & Evaluation</option>
                            <option>Other</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">Cover Letter</label>
                        <textarea rows="6" class="form-textarea" placeholder="Tell us why you're interested in interning with Humanity Praxis and what you hope to learn..."></textarea>
                    </div>
                    <div class="flex space-x-4">
                        <button type="submit" class="bg-primary hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                            Submit Application
                        </button>
                        <button type="button" onclick="closeModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded-lg transition duration-300">
                            Cancel
                        </button>
                    </div>
                </form>
            `;

            modal.classList.add('active');
        }

        function openPartnershipForm(type) {
            const modal = document.getElementById('application-modal');
            const content = document.getElementById('modal-content');

            content.innerHTML = `
                <h3 class="text-2xl font-bold text-gray-800 mb-6">Partnership Inquiry - ${type.charAt(0).toUpperCase() + type.slice(1)}</h3>
                <form class="space-y-4">
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">Organization Name *</label>
                        <input type="text" required class="form-input">
                    </div>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-gray-700 font-medium mb-2">Contact Person *</label>
                            <input type="text" required class="form-input">
                        </div>
                        <div>
                            <label class="block text-gray-700 font-medium mb-2">Position/Title *</label>
                            <input type="text" required class="form-input">
                        </div>
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">Email *</label>
                        <input type="email" required class="form-input">
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">Organization Website</label>
                        <input type="url" class="form-input">
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">Partnership Interest</label>
                        <textarea rows="4" class="form-textarea" placeholder="Describe your organization and the type of partnership you're interested in..."></textarea>
                    </div>
                    <div>
                        <label class="block text-gray-700 font-medium mb-2">Proposed Collaboration</label>
                        <textarea rows="4" class="form-textarea" placeholder="Share your ideas for how we might work together..."></textarea>
                    </div>
                    <div class="flex space-x-4">
                        <button type="submit" class="bg-primary hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition duration-300">
                            Submit Inquiry
                        </button>
                        <button type="button" onclick="closeModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded-lg transition duration-300">
                            Cancel
                        </button>
                    </div>
                </form>
            `;

            modal.classList.add('active');
        }

        // Additional modal functions would go here for job applications, etc.
        function viewJobDetails(jobId) {
            // Implementation for viewing job details
            console.log('View job details for:', jobId);
        }

        function applyForJob(jobId) {
            // Implementation for job application
            console.log('Apply for job:', jobId);
        }

        function openGeneralApplicationForm() {
            // Implementation for general application
            console.log('Open general application form');
        }
    </script>
</body>
</html>